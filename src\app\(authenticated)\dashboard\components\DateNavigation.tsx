"use client"

import { format } from 'date-fns';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface DateNavigationProps {
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  navigateWeek: (direction: 'prev' | 'next') => void;
  onAddTimeBlock: () => void;
}

export function DateNavigation({ 
  selectedDate, 
  setSelectedDate, 
  navigateWeek, 
  onAddTimeBlock 
}: DateNavigationProps) {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 bg-background sticky top-0 z-10 py-2">
      <div>
        <p className="text-muted-foreground font-medium">
          {format(selectedDate, 'MMMM d, yyyy')}
        </p>
      </div>
      <div className="flex flex-wrap items-center gap-1 w-full sm:w-auto">
        <div className="flex items-center gap-1">
          <Button variant="outline" size="sm" onClick={() => navigateWeek('prev')}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => navigateWeek('next')}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <CalendarIcon className="h-4 w-4" />
              <span className="hidden xs:inline">Select date</span>
              <span className="xs:hidden">Date</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={(date) => date && setSelectedDate(date)}
              initialFocus
            />
          </PopoverContent>
        </Popover>
        <Button
          onClick={onAddTimeBlock}
          size="sm"
          className="flex-1 sm:flex-none justify-center"
        >
          <Plus className="h-4 w-4 mr-1 sm:mr-2" />
          <span className="hidden xs:inline">Add Time Block</span>
          <span className="xs:hidden">Add</span>
        </Button>
      </div>
    </div>
  );
}
