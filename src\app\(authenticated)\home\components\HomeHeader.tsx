"use client"

import { format } from 'date-fns';
import { Plus, BetweenHorizontalStart, Tag } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';

interface HomeHeaderProps {
  selectedDate: Date;
  onAddTimeBlock: () => void;
  activeTab: string;
}

export function HomeHeader({ selectedDate, onAddTimeBlock, activeTab }: HomeHeaderProps) {
  return (
    <div className="mx-2 mt-2 sm:sticky sm:top-0 sm:z-10 sm:bg-background/95 sm:backdrop-blur-sm sm:pb-2">
      {/* Mobile: Date and Add Note Button Row */}
      <div className="flex justify-between items-center px-2 sm:hidden">
        <h2 className="text-base font-semibold">
          {format(selectedDate, "EEE, MMM d, yyyy")}
        </h2>

        <Button
          onClick={onAddTimeBlock}
          size="sm"
          variant="outline"
          className="h-8 border border-primary/30 hover:bg-primary/10 text-primary rounded-full px-3"
        >
          <Plus className="h-3.5 w-3.5 mr-1" />
          <span className="text-xs font-medium">Add Notes</span>
        </Button>
      </div>

      {/* Desktop: All components in one line */}
      <div className="hidden sm:flex justify-between items-center px-2 py-1">
        <h2 className="text-lg font-semibold">
          {format(selectedDate, "EEE, MMM d, yyyy")}
        </h2>

        <div className="mx-4">
          <TabsList className="h-6 bg-muted/30">
            <TabsTrigger
              value="grid"
              className="px-3 h-6"
            >
              <BetweenHorizontalStart className="h-4 w-4 mr-2" />
              <span>Grid View</span>
            </TabsTrigger>
            <TabsTrigger
              value="list"
              className="px-3 h-6"
            >
              <Tag className="h-4 w-4 mr-2" />
              <span>My Todos</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <Button
          onClick={onAddTimeBlock}
          size="sm"
          variant="outline"
          className="h-8 border border-primary/30 hover:bg-primary/10 text-primary rounded-full px-3"
        >
          <Plus className="h-3.5 w-3.5 mr-1" />
          <span className="text-xs font-medium">Add Notes</span>
        </Button>
      </div>

      {/* Mobile: Tab Navigation */}
      <div className="sm:hidden w-full mt-2">
        <TabsList className="w-full flex bg-muted/30 h-7">
          <TabsTrigger
            value="grid"
            className="flex-1 min-w-[60px] h-6"
          >
            <BetweenHorizontalStart className="h-4 w-4 mr-1" />
            <span>Grid</span>
          </TabsTrigger>
          <TabsTrigger
            value="list"
            className="flex-1 min-w-[60px] h-6"
          >
            <Tag className="h-4 w-4 mr-1" />
            <span>My Todos</span>
          </TabsTrigger>
        </TabsList>
      </div>
    </div>
  );
}
