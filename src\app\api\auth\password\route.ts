import { NextRequest } from 'next/server';
import { validatePasswordChange } from '@/app/api/_validators/auth.validator';
import { authController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * PUT /api/auth/password - Change user password
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Validate request
    const validationResult = await validatePasswordChange(req);
    if ('status' in validationResult) {
      return validationResult;
    }

    // Process password change
    return authController.changePassword(user.id, validationResult.data);
  } catch (error) {
    logger.error('Password change error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
