/**
 * Central type definitions for client components
 *
 * This file is the single source of truth for client-side type definitions.
 * Server-side types should be imported from lib/server-utils.ts
 */

// Auth related types
export type {
  JwtPayload,
  AuthResult,
  LoginRequest,
  RegisterRequest,
  PasswordChangeRequest,
  UserProfile,
  User
} from '@/types/auth';

// TimeBlock related types
export type {
  TimeBlock,
  TimeBlockFormData,
  CreateTimeBlockRequest,
  UpdateTimeBlockRequest
} from '@/types/timeblock';

// Category related types
export type {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest
} from '@/types/category';

// User preferences type
export type { IUserPreferences as UserPreferences } from '@/db/models/User';
