import { NextRequest } from 'next/server';
import * as jwt from 'jsonwebtoken';
import { JWT_SECRET } from '@/config/auth';
import { JwtPayload, AuthResult } from '@/types/auth';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';

/**
 * Get JWT token from request
 * @param req Next.js request
 * @returns JWT token or null
 */
export function getToken(req: NextRequest): string | null {
  // Try to get token from Authorization header
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // If not in header, try cookies from the request
  return req.cookies.get('token')?.value || null;
}

/**
 * Verify authentication
 * @param req Next.js request
 * @returns Authentication result
 */
export function verifyAuth(req: NextRequest): AuthResult {
  try {
    const token = getToken(req);

    if (!token) {
      return { authenticated: false };
    }

    const decoded = (jwt as any).verify(token, JWT_SECRET) as JwtPayload;
    return {
      authenticated: true,
      user: decoded
    };
  } catch (error) {
    // Check error type by name since instanceof might not work with the import style
    if (error instanceof Error) {
      if (error.name === 'TokenExpiredError') {
        throw ApiError.unauthorized('Token expired', ErrorCode.TOKEN_EXPIRED);
      } else if (error.name === 'JsonWebTokenError') {
        throw ApiError.unauthorized('Invalid token', ErrorCode.INVALID_TOKEN);
      }
    }

    return { authenticated: false };
  }
}

/**
 * Generate JWT token
 * @param payload Token payload
 * @param expiresIn Token expiration time
 * @returns JWT token
 */
export function generateToken(payload: JwtPayload, expiresIn: string): string {
  // Use any type to bypass TypeScript checking
  return (jwt as any).sign(payload, JWT_SECRET, { expiresIn });
}
