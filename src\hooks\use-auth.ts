"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

// Cache the auth status in memory
let cachedAuth = {
  isAuthenticated: false,
  lastChecked: 0
};

const AUTH_CACHE_TIME = 5 * 60 * 1000; // 5 minutes

export function useAuth({ redirectTo }: { redirectTo?: string } = {}) {
  const [isAuthenticated, setIsAuthenticated] = useState(cachedAuth.isAuthenticated);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const checkAuth = useCallback(async () => {
    const now = Date.now();

    // Return cached value if valid
    if (now - cachedAuth.lastChecked < AUTH_CACHE_TIME) {
      setIsAuthenticated(cachedAuth.isAuthenticated);
      setIsLoading(false);
      return;
    }

    try {
      // Check the API to verify the cookie as the source of truth
      const response = await fetch('/api/auth/profile', {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const userData = await response.json();
        localStorage.setItem('user', JSON.stringify({
          ...userData.data || userData,
          lastLogin: new Date().toISOString()
        }));

        cachedAuth = { isAuthenticated: true, lastChecked: now };
        setIsAuthenticated(true);
      } else {
        cachedAuth = { isAuthenticated: false, lastChecked: now };
        setIsAuthenticated(false);
        if (redirectTo) router.push(redirectTo);
      }
    } catch (error) {
      // Try localStorage as fallback if network request fails
      try {
        const user = localStorage.getItem('user');
        if (user) {
          cachedAuth = { isAuthenticated: true, lastChecked: now };
          setIsAuthenticated(true);
        } else {
          cachedAuth = { isAuthenticated: false, lastChecked: now };
          setIsAuthenticated(false);
          if (redirectTo) router.push(redirectTo);
        }
      } catch (e) {
        cachedAuth = { isAuthenticated: false, lastChecked: now };
        setIsAuthenticated(false);
        if (redirectTo) router.push(redirectTo);
      }
    } finally {
      setIsLoading(false);
    }
  }, [redirectTo, router]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const logout = async () => {
    try {
      // Call logout API to clear the cookie
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      // Clear all authentication data from localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('userProfile');
      localStorage.removeItem('timeTrackerPreferences');

      // Update authentication state
      cachedAuth = { isAuthenticated: false, lastChecked: Date.now() };
      setIsAuthenticated(false);

      // Redirect to login page
      router.push('/auth/login');
    } catch (error) {
      // Even if API call fails, clear localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('userProfile');
      localStorage.removeItem('timeTrackerPreferences');

      // Update authentication state
      cachedAuth = { isAuthenticated: false, lastChecked: Date.now() };
      setIsAuthenticated(false);

      // Redirect to login page
      router.push('/auth/login');
    }
  };

  return {
    isAuthenticated,
    isLoading,
    logout,
    checkAuth
  };
}
