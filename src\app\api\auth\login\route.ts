import { NextRequest } from 'next/server';
import { validateLogin } from '@/app/api/_validators/auth.validator';
import { authController } from '@/core/controllers';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * POST /api/auth/login - Login a user
 */
export async function POST(req: NextRequest) {
  try {
    // Validate request
    const validationResult = await validateLogin(req);
    if ('status' in validationResult) {
      return validationResult;
    }

    // Process login
    return authController.login(validationResult.data);
  } catch (error) {
    logger.error('Login error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
