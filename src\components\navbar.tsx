"use client"

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Settings, LogOut, Menu, User, LayoutDashboard, ChevronDown, Home, Download, CalendarClock } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from '@/components/ui/sheet';
import { ModeToggle } from '@/components/mode-toggle';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/use-auth';
import { useProfile } from '@/hooks/use-profile';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { PWAInstaller } from '@/components/pwa-installer';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function Navbar() {
  const pathname = usePathname();
  const { logout } = useAuth({});
  const { profile, refreshProfile } = useProfile();

  // Refresh profile data when the component mounts
  useEffect(() => {
    refreshProfile();
  }, [refreshProfile]);

  const handleLogout = () => {
    logout();
    toast.success('Logged out successfully');
  };

  const navItems = [
    { href: "/home", label: "Home", IconComponent: Home, iconProps: { mobile: "mr-3 h-5 w-5", desktop: "mr-2 h-4 w-4" } },
    { href: "/routine", label: "Routine", IconComponent: CalendarClock, iconProps: { mobile: "mr-3 h-5 w-5", desktop: "mr-2 h-4 w-4" } },
    { href: "/dashboard", label: "Dashboard", IconComponent: LayoutDashboard, iconProps: { mobile: "mr-3 h-5 w-5", desktop: "mr-2 h-4 w-4" } },
    { href: "/dashboard/settings", label: "Settings", IconComponent: Settings, iconProps: { mobile: "mr-3 h-5 w-5", desktop: "mr-2 h-4 w-4" } },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur shadow-sm">
      <div className="container flex h-12 md:h-16 items-center px-2 md:px-6">
        {/* Desktop logo - hidden on mobile */}
        <div className="mr-6 hidden md:flex">
          <Link href="/home" className="flex items-center gap-2 font-bold">
            <div className="flex items-center">

            </div>
            <span className="text-3xl">NoteHour</span>
          </Link>
        </div>

        {/* Mobile navbar - 3 sections: hamburger menu, logo, mode toggle */}
        <div className="flex w-full items-center justify-between md:hidden">
          {/* 1. Hamburger menu on left */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 pt-6 border-r">
              <div className="flex items-center gap-2 font-bold px-6 mb-6">
                <span className="text-xl">NoteHour</span>
              </div>
              <Separator className="mb-4" />
              <div className="grid gap-1 px-2">
                {navItems.map(({ href, label, IconComponent, iconProps }) => (
                  <Link href={href} key={href}>
                    <Button variant={pathname === href ? "default" : "ghost"} className="w-full justify-start h-11 px-4 font-medium">
                      <IconComponent className={iconProps.mobile} />
                      {label}
                    </Button>
                  </Link>
                ))}
              </div>
              {!pathname.includes("/demo") && (
                <>
                  <Separator className="my-4 mt-5" />
                  <div className="px-6 py-2">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-9 w-9">
                        <AvatarFallback className="bg-primary/10 text-primary">
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{profile?.name || 'User'}</p>
                        <p className="text-xs text-muted-foreground">{profile?.email || ''}</p>
                      </div>
                    </div>
                  </div>
                  <div className="px-2 mt-4 space-y-2">
                    <Button variant="outline" className="w-full justify-start h-11 px-4 font-medium" onClick={handleLogout}>
                      <LogOut className="mr-3 h-5 w-5" />
                      Log Out
                    </Button>
                    <div className="flex justify-center py-2">
                      <PWAInstaller />
                    </div>
                  </div>
                </>
              )}
            </SheetContent>
          </Sheet>

          {/* 2. Logo in center */}
          <div className="flex justify-center">
            <Link href="/home" className="flex items-center gap-2 font-bold">
              <span className="text-xl">NoteHour</span>
            </Link>
          </div>

          {/* 3. Mode toggle and PWA installer on right */}
          <div className="flex items-center gap-2">
            <PWAInstaller />
            <ModeToggle />
          </div>
        </div>

        {/* Desktop navbar - hidden on mobile */}
        <div className="hidden md:flex w-full items-center justify-between">
          <nav className="flex items-center gap-4 text-sm">
            <div className="flex gap-2">
              {navItems.map(({ href, label, IconComponent, iconProps }) => (
                <Link href={href} key={href}>
                  <Button variant={pathname === href ? "default" : "ghost"} className="h-10 px-4 font-medium">
                    <IconComponent className={iconProps.desktop} />
                    {label}
                  </Button>
                </Link>
              ))}
            </div>
          </nav>
          <div className="flex items-center gap-4">
            <PWAInstaller />
            <ModeToggle />
            {!pathname.includes("/demo") && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 px-2 rounded-full h-10">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-primary/10 text-primary">
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium text-sm hidden md:inline-block">
                      {profile?.name || 'User'}
                    </span>
                    <ChevronDown className="h-4 w-4 text-muted-foreground hidden md:inline-block" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="flex items-center justify-start gap-2 p-2">
                    <Avatar className="h-8 w-8 md:hidden">
                      <AvatarFallback className="bg-primary/10 text-primary">
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col space-y-0.5">
                      <p className="text-sm font-medium">{profile?.name || 'User'}</p>
                      <p className="text-xs text-muted-foreground truncate">{profile?.email || ''}</p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <Link href="/dashboard/settings">
                    <DropdownMenuItem>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                  </Link>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
