"use client"

import React, { useState, useEffect, useRef } from 'react';
import { parseISO, isToday, format } from 'date-fns';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { TimeBlock } from '@/lib/types';
import { useTimeBlocks } from '@/hooks/use-time-blocks';
import { usePreferences } from '@/hooks/use-preferences';

// Import components
import { HomeHeader } from './components';
import { GridViewTab } from './components/GridViewTab';
import { TimeBlockFormWrapper } from './components/TimeBlockFormWrapper';
import { TodoTab } from "./components/TodoTab";

export default function HomePage() {
  const [selectedDate] = useState<Date>(new Date());
  const [activeTab, setActiveTab] = useState("grid");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [blockToEdit, setBlockToEdit] = useState<TimeBlock | undefined>(undefined);
  const { timeBlocks, loading, addTimeBlock, updateTimeBlock, deleteTimeBlock, refreshTimeBlocks } = useTimeBlocks();
  const [filteredBlocks, setFilteredBlocks] = useState<TimeBlock[]>([]);
  const [allTodos, setAllTodos] = useState<TimeBlock[]>([]);
  const [timeInterval, setTimeInterval] = useState<string>("60");
  const { preferences } = usePreferences();
  const [convertingRoutines, setConvertingRoutines] = useState(false);

  // Update time interval from preferences
  useEffect(() => {
    if (preferences?.timeInterval) {
      setTimeInterval(preferences.timeInterval);
    }
  }, [preferences]);

  // Convert routines to todos when the page loads or when returning to this page
  // Use a ref to track if we've already done the initial load
  const initialLoadDoneRef = React.useRef(false);

  useEffect(() => {
    // We'll use a timestamp to prevent too frequent conversions
    const lastConversionTime = sessionStorage.getItem('lastRoutineConversionTime');
    const now = Date.now();
    const timeSinceLastConversion = lastConversionTime ? now - parseInt(lastConversionTime) : Infinity;

    // Only run if it's been at least 30 seconds since the last conversion
    // This prevents excessive API calls while still ensuring timely updates
    const shouldRunConversion = timeSinceLastConversion > 30000;

    // If we've already done the initial load and we shouldn't run conversion, just return
    if (initialLoadDoneRef.current && !shouldRunConversion) {
      return;
    }

    const convertRoutinesToTodos = async () => {
      // If we're already converting, don't start another conversion
      if (convertingRoutines) {
        return;
      }

      try {
        setConvertingRoutines(true);

        // Only make the API call if we should run conversion
        if (shouldRunConversion) {
          const response = await fetch('/api/routines/convert-to-todos', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            },
            credentials: 'include'
          });

          if (response.ok) {
            // Store the current timestamp as the last conversion time
            sessionStorage.setItem('lastRoutineConversionTime', now.toString());

            const result = await response.json();
            console.log('Converted routines to todos:', result);
          }
        }

        // Always refresh time blocks after conversion to ensure UI is up to date
        // But only do it once per page load
        if (!initialLoadDoneRef.current) {
          await refreshTimeBlocks(true);
          initialLoadDoneRef.current = true;
        }
      } catch (error) {
        console.error('Error converting routines to todos:', error);
      } finally {
        setConvertingRoutines(false);
      }
    };

    // Small delay to prevent this from running immediately on page load
    // This helps prevent race conditions with other initialization code
    const timer = setTimeout(() => {
      convertRoutinesToTodos();
    }, 300);

    return () => clearTimeout(timer);
  }, [refreshTimeBlocks, convertingRoutines]);

  useEffect(() => {
    if (timeBlocks) {
      // Format the selected date for filtering
      const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');

      // Filter blocks for the grid view based on the selected date
      const filtered = timeBlocks.filter(block => {
        // Show time blocks for the selected date
        if (block.date === selectedDateStr) {
          return true;
        }

        // For routine tasks, ensure they appear on the correct date
        if (block.routineId) {
          const blockDate = new Date(block.date);
          const selected = new Date(selectedDate);

          // Only show routine tasks that match the selected date
          if (blockDate.getDate() === selected.getDate() &&
              blockDate.getMonth() === selected.getMonth() &&
              blockDate.getFullYear() === selected.getFullYear()) {
            return true;
          }
        }

        return false;
      });
      setFilteredBlocks(filtered);

      // Get all todos for the todo tab
      // Only show todos for today and future dates (not past dates)
      const today = new Date();
      const todayStr = format(today, 'yyyy-MM-dd');

      const todos = timeBlocks
        .filter(block => {
          if (!block.isTodo) return false;

          const blockDate = parseISO(block.date);
          const blockDateStr = format(blockDate, 'yyyy-MM-dd');

          // Only show todos for today or future dates
          return blockDateStr >= todayStr;
        })
        .sort((a, b) => {
          const dateA = parseISO(a.date);
          const dateB = parseISO(b.date);

          // If one is today and the other isn't, today comes first
          const aTodayStatus = isToday(dateA) ? 0 : 1;
          const bTodayStatus = isToday(dateB) ? 0 : 1;

          if (aTodayStatus !== bTodayStatus) {
            return aTodayStatus - bTodayStatus;
          }

          // For routine todos, they should come first within the same day
          const aIsRoutine = a.note.startsWith('[Routine]') ? 0 : 1;
          const bIsRoutine = b.note.startsWith('[Routine]') ? 0 : 1;

          if (aIsRoutine !== bIsRoutine) {
            return aIsRoutine - bIsRoutine;
          }

          // Otherwise sort by date
          return dateA.getTime() - dateB.getTime();
        });

      setAllTodos(todos);
    }
  }, [timeBlocks, selectedDate]);

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-0">
      {/* Header with date display, tabs and add button */}
      <HomeHeader
        selectedDate={selectedDate}
        onAddTimeBlock={() => setIsFormOpen(true)}
        activeTab={activeTab}
      />

      {/* Grid View Tab */}
      <TabsContent value="grid" className="mt-4 px-2">
        <GridViewTab
          selectedDate={selectedDate}
          filteredBlocks={filteredBlocks}
          onAddBlock={() => setIsFormOpen(true)}
          onEditBlock={(block) => {
            setBlockToEdit(block);
            setIsFormOpen(true);
          }}
          onDeleteBlock={deleteTimeBlock}
          refreshTimeBlocks={refreshTimeBlocks}
          onUpdateBlock={updateTimeBlock}
        />
      </TabsContent>

      {/* Todo Tab */}
      <TabsContent value="list" className="mt-4 px-2">
        <TodoTab
          selectedDate={selectedDate}
          onAddBlock={() => {
            // When adding from todo tab, we'll set isTodo to true in the form
            setIsFormOpen(true);
          }}
          onEditBlock={(block: TimeBlock) => {
            setBlockToEdit(block);
            setIsFormOpen(true);
          }}
          onDeleteBlock={async (id: string) => {
            try {
              const success = await deleteTimeBlock(id);
              if (success) {
                // Force refresh to ensure both views are updated
                await refreshTimeBlocks(true);
              }
              return success;
            } catch (error) {
              console.error("Error deleting time block from todo tab:", error);
              return false;
            }
          }}
          refreshTimeBlocks={refreshTimeBlocks}
        />
      </TabsContent>

      {/* Time Block Form */}
      <TimeBlockFormWrapper
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setBlockToEdit(undefined);
        }}
        timeBlock={blockToEdit}
        selectedDate={selectedDate}
        updateTimeBlock={updateTimeBlock}
        addTimeBlock={addTimeBlock}
        refreshTimeBlocks={refreshTimeBlocks}
        activeTab={activeTab}
      />
    </Tabs>
  );
}
