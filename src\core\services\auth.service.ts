import { NextResponse } from 'next/server';
import mongoose from 'mongoose';
import { userRepository, categoryRepository } from '@/core/repositories';
import { JWT_SECRET, JWT_EXPIRY, COOKIE_OPTIONS } from '@/config/auth';
import { LoginRequest, RegisterRequest, PasswordChangeRequest, JwtPayload } from '@/types/auth';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import { generateToken } from '@/utils/auth';
import { successResponse } from '@/utils/response';
import logger from '@/utils/logger';

/**
 * Authentication service
 */
export class AuthService {
  /**
   * Register a new user
   * @param data Registration data
   * @returns Success message or error
   */
  async register(data: RegisterRequest): Promise<{ message: string; status: number }> {
    try {
      const { name, email, password } = data;
      logger.info(`Registration attempt for email: ${email}`);

      // Check if user already exists
      logger.info('Checking if user already exists');
      const existingUser = await userRepository.findByEmail(email);
      if (existingUser) {
        logger.info(`User with email ${email} already exists`);
        throw ApiError.conflict('User already exists', ErrorCode.USER_ALREADY_EXISTS);
      }

      // Create new user
      logger.info(`Creating new user with email: ${email}`);
      const newUser = await userRepository.create({
        name,
        email,
        password: password.trim(),
      });
      if (!newUser || !('_id' in newUser)) {
        throw ApiError.internal('Failed to create user', ErrorCode.INTERNAL_SERVER_ERROR);
      }

      logger.info(`User with email ${email} created successfully`);

      // Create default categories for the new user
      try {
        const userId = (newUser._id as mongoose.Types.ObjectId).toString();
        logger.info(`Creating default categories for user ${userId}`);

        // Define default categories
        const defaultCategories = [
          { name: 'Work', color: '#4f46e5' },
          { name: 'Personal', color: '#10b981' },
          { name: 'Study', color: '#f59e0b' },
          { name: 'Health', color: '#ef4444' },
          { name: 'Leisure', color: '#8b5cf6' }
        ];

        // Create each default category
        for (const category of defaultCategories) {
          await categoryRepository.create(userId, category);
        }

        logger.info(`Default categories created for user ${userId}`);
      } catch (categoryError) {
        // Log the error but don't fail registration if category creation fails
        logger.error('Error creating default categories:', categoryError);
      }

      return { message: 'User registered successfully', status: 201 };
    } catch (error) {
      logger.error('Registration error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Login a user
   * @param data Login data
   * @returns Response with token and user data or error
   */
  async login(data: LoginRequest): Promise<NextResponse> {
    try {
      const { email, password } = data;

      // Ensure password is trimmed
      const trimmedPassword = password.trim();

      // Find user by email
      let user;
      try {
        user = await userRepository.findByEmail(email);
      } catch (dbError) {
        logger.error('Database error when finding user');
        throw ApiError.internal(
          'Database error when finding user',
          ErrorCode.DATABASE_ERROR
        );
      }

      if (!user) {
        throw ApiError.unauthorized('Invalid credentials', ErrorCode.INVALID_CREDENTIALS);
      }

      // Check password
      let isMatch = false;
      try {
        // Use the trimmed password for comparison
        isMatch = await user.comparePassword(trimmedPassword);
      } catch (passwordError) {
        throw ApiError.internal(
          'Error comparing password',
          ErrorCode.INTERNAL_SERVER_ERROR
        );
      }

      if (!isMatch) {
        throw ApiError.unauthorized('Invalid credentials', ErrorCode.INVALID_CREDENTIALS);
      }

      // Generate JWT token
      const userId = user._id instanceof mongoose.Types.ObjectId
        ? user._id.toString()
        : typeof user._id === 'string'
          ? user._id
          : String(user._id);

      let token;
      try {
        token = generateToken(
          { id: userId, email: user.email, name: user.name },
          JWT_EXPIRY
        );
      } catch (tokenError) {
        throw ApiError.internal(
          'Error generating authentication token',
          ErrorCode.INTERNAL_SERVER_ERROR
        );
      }

      // Create response with cookie
      const responseData = {
        user: {
          id: userId,
          name: user.name,
          email: user.email,
          preferences: user.preferences,
        },
        token,
      };
      const response = successResponse(responseData);

      // Set cookie
      try {
        response.cookies.set('token', token, COOKIE_OPTIONS);
      } catch (cookieError) {
        // Continue anyway since localStorage is a fallback
        logger.error('Error setting cookie');
      }

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal(
        error instanceof Error ? error.message : 'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Logout a user
   * @returns Response with success message
   */
  logout(): NextResponse {
    try {
      // Create response
      const response = successResponse({ message: 'Logged out successfully' });

      // Clear cookie
      response.cookies.set('token', '', {
        ...COOKIE_OPTIONS,
        maxAge: 0,
      });

      return response;
    } catch (error) {
      logger.error('Logout error:', error);
      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get user profile
   * @param userId User ID
   * @returns User profile or error
   */
  async getProfile(userId: string) {
    try {
      const profile = await userRepository.getProfile(userId);

      if (!profile) {
        throw ApiError.notFound('User not found', ErrorCode.USER_NOT_FOUND);
      }

      return profile;
    } catch (error) {
      logger.error('Get profile error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update user profile
   * @param userId User ID
   * @param data Profile data
   * @returns Updated profile or error
   */
  async updateProfile(userId: string, data: any) {
    try {
      console.log('authService.updateProfile: Received data:', data);
      const { name, email, preferences } = data;

      // Find user
      const user = await userRepository.findById(userId);
      if (!user) {
        throw ApiError.notFound('User not found', ErrorCode.USER_NOT_FOUND);
      }

      console.log('authService.updateProfile: Current user preferences:', user.preferences);
      console.log('authService.updateProfile: New preferences to merge:', preferences);

      // Update fields
      if (name) user.name = name;
      if (email) user.email = email;
      if (preferences) {
        user.preferences = { ...user.preferences, ...preferences };
        console.log('authService.updateProfile: Merged preferences:', user.preferences);
      }

      await user.save();
      console.log('authService.updateProfile: User saved successfully');

      const userIdStr = user._id instanceof mongoose.Types.ObjectId
        ? user._id.toString()
        : typeof user._id === 'string'
          ? user._id
          : String(user._id);

      const result = {
        id: userIdStr,
        name: user.name,
        email: user.email,
        preferences: user.preferences,
      };

      console.log('authService.updateProfile: Returning result:', result);
      return result;
    } catch (error) {
      logger.error('Update profile error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Change user password
   * @param userId User ID
   * @param data Password change data
   * @returns Success or error
   */
  async changePassword(userId: string, data: PasswordChangeRequest) {
    try {
      const { currentPassword, newPassword } = data;

      // Find user
      const user = await userRepository.findById(userId);
      if (!user) {
        throw ApiError.notFound('User not found', ErrorCode.USER_NOT_FOUND);
      }

      // Check current password
      const isMatch = await user.comparePassword(currentPassword);
      if (!isMatch) {
        throw ApiError.badRequest('Current password is incorrect', ErrorCode.INVALID_PASSWORD);
      }

      // Update password
      user.password = newPassword;
      await user.save();

      return { success: true };
    } catch (error) {
      logger.error('Change password error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}
