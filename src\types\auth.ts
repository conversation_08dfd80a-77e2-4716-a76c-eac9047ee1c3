/**
 * JWT payload interface
 */
export interface JwtPayload {
  id: string;
  email: string;
  name: string;
}

/**
 * Authentication result interface
 */
export interface AuthResult {
  authenticated: boolean;
  user?: JwtPayload;
}

/**
 * Login request interface
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Register request interface
 */
export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

/**
 * Password change request interface
 */
export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
}

/**
 * User interface (simplified for frontend use)
 */
export interface User {
  id: string;
  name: string;
  email: string;
  createdAt?: string;
}

/**
 * User profile interface
 */
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  preferences: {
    timeInterval: string;
    startHour: string;
    endHour: string;
    timeFormat: string;
    darkMode: boolean;
    syncEnabled: boolean;
    emailNotifications: boolean;
    customTimeBlocks?: Array<{ startTime: string; endTime: string }>;
    useCustomTimeBlocks?: boolean;
  };
}
