import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/config/database";
import { verifyAuth } from "@/utils/auth";
import mongoose from "mongoose";
import { format, isSameDay } from "date-fns";
import logger from "@/utils/logger";

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  try {
    // Verify user is authenticated
    const { authenticated, user } = verifyAuth(req);
    if (!authenticated || !user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    const today = new Date();
    const todayDateString = format(today, 'yyyy-MM-dd');
    
    // For the next 14 days, we'll create todos - extending the range to ensure routines show up properly
    const next14Days = [];
    for (let i = 0; i < 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      next14Days.push({
        date: format(date, 'yyyy-MM-dd'),
        dayOfWeek: date.getDay(), // Sunday: 0, Monday: 1, ...
        dateObj: new Date(date) // Keep the full date object for comparisons
      });
    }

    // Get all routines for the user that are active (we'll filter by day later)
    // Also get the category data to pass to the todos
    const routines = await mongoose.connection
      .collection("routines")
      .aggregate([
        {
          $match: {
            userId: user.id,
            // Make sure the routine is enabled (if it has an isEnabled field)
            $or: [
              { isEnabled: { $exists: false } }, // No isEnabled field
              { isEnabled: true }                // isEnabled is true
            ]
          }
        },
        {
          $lookup: {
            from: "categories",
            localField: "categoryId",
            foreignField: "_id",
            as: "categoryInfo"
          }
        },
        {
          $addFields: {
            categoryName: { $arrayElemAt: ["$categoryInfo.name", 0] }
          }
        }
      ])
      .toArray();

    if (routines.length === 0) {
      return NextResponse.json({
        message: "No active routines found",
        converted: 0
      });
    }

    // Instead of deleting and recreating everything, let's be more efficient
    // 1. First, get all existing routine todos for the next 14 days
    const datesToCheck = next14Days.map(d => d.date);
    const existingTodos = await mongoose.connection
      .collection("timeblocks")
      .find({
        userId: new mongoose.Types.ObjectId(user.id),
        date: { $in: datesToCheck },
        routineId: { $exists: true },
        isTodo: true
      }).toArray();
      
    // 2. Create a map of existing todos by routineId and date for quick lookup
    const existingTodoMap = new Map();
    existingTodos.forEach(todo => {
      const key = `${todo.routineId.toString()}_${todo.date}`;
      existingTodoMap.set(key, todo);
    });
    
    // Track todos to delete - we'll delete them after creating new ones
    const todosToDelete = new Set(existingTodos.map(t => t._id.toString()));

    // Create todos for each routine on its scheduled days
    const createdTodos = [];

    // For each routine, create todos on the days it's scheduled for
    for (const routine of routines) {
      // Get the days this routine is scheduled for (as array of numbers)
      const scheduledDays = Array.isArray(routine.days) ? routine.days : [];
      
      // Skip routines with no scheduled days
      if (scheduledDays.length === 0) continue;
      
      // Create a todo for each day in the next 14 days where this routine is scheduled
      for (const dayInfo of next14Days) {
        // Check if this routine runs on this day of the week
        if (scheduledDays.includes(dayInfo.dayOfWeek)) {
          const note = `[Routine] ${routine.title}${routine.note ? `: ${routine.note}` : ''}`;

          // Generate a title that includes routine name
          const title = routine.title || 'Routine Task';

          // For MyTodos section, we only want to show the closest future occurrence of each routine
          // Check if this is the first occurrence of this routine in our date range
          const isFirstOccurrenceInRange = next14Days
            .filter(d => scheduledDays.includes(d.dayOfWeek))
            .findIndex(d => d.date === dayInfo.date) === 0;
            
          const todoData = {
            userId: new mongoose.Types.ObjectId(user.id),
            date: dayInfo.date, // Use the date from our 14-day array
            startTime: routine.startTime,
            endTime: routine.endTime,
            title: title, // Make sure we have a title
            note: note,
            category: routine.categoryId ? new mongoose.Types.ObjectId(routine.categoryId) : null,
            categoryData: routine.categoryId ? { id: routine.categoryId, name: routine.categoryName } : null,
            routineId: routine._id,
            isTodo: true, // Explicitly mark as todo
            isCompleted: false,
            // Set isToday flag for today's tasks to ensure they show up in the home grid view
            isToday: isSameDay(dayInfo.dateObj, today),
            // For MyTodos view, only show the first occurrence of each routine
            showInMyTodos: isFirstOccurrenceInRange,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          // Check if this todo already exists
          const todoKey = `${routine._id.toString()}_${dayInfo.date}`;
          const existingTodo = existingTodoMap.get(todoKey);
          
          if (existingTodo) {
            // If it exists, keep it (remove from delete set)
            todosToDelete.delete(existingTodo._id.toString());
            
            // Only update if needed (for example, if title changed)
            if (existingTodo.title !== title || existingTodo.note !== note) {
              await mongoose.connection
                .collection("timeblocks")
                .updateOne(
                  { _id: existingTodo._id },
                  { $set: {
                    title: title,
                    note: note,
                    updatedAt: new Date()
                  }}
                );
            }
            
            // Add to createdTodos for consistency in return value
            createdTodos.push({
              id: existingTodo._id.toString(),
              ...todoData,
              userId: user.id,
              category: routine.categoryId || null,
              routineId: routine._id.toString()
            });
          } else {
            // If it doesn't exist, create it
            const result = await mongoose.connection
              .collection("timeblocks")
              .insertOne(todoData);
  
            createdTodos.push({
              id: result.insertedId.toString(),
              ...todoData,
              userId: user.id,
              category: routine.categoryId || null,
              routineId: routine._id.toString()
            });
          }
        }
      }
    }

    // Now delete any old todos that weren't reused
    if (todosToDelete.size > 0) {
      const idsToDelete = Array.from(todosToDelete).map(id => new mongoose.Types.ObjectId(id));
      await mongoose.connection
        .collection("timeblocks")
        .deleteMany({
          _id: { $in: idsToDelete }
        });
      
      console.log(`Deleted ${todosToDelete.size} outdated routine todos`);
    }
    
    console.log(`Created/updated ${createdTodos.length} todos from routines`);

    return NextResponse.json({
      message: `Successfully converted ${createdTodos.length} routines to todos`,
      converted: createdTodos.length,
      todos: createdTodos
    });
  } catch (error) {
    logger.error("Error converting routines to todos:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
