"use client"

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { UseFormReturn } from 'react-hook-form';
import { PreferencesFormValues } from '../../schemas';

interface TimeIntervalPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function TimeIntervalPreference({ form }: TimeIntervalPreferenceProps) {
  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <FormField
        control={form.control}
        name="timeInterval"
        render={({ field }) => (
          <FormItem className="mb-0">
            <FormLabel className="text-sm font-medium">Default Time Interval</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-wrap gap-2 mt-1"
              >
                {/* Minute-based intervals */}
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="15" id="15-min" />
                  <FormLabel htmlFor="15-min" className="font-normal cursor-pointer text-sm">15 minutes</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="30" id="30-min" />
                  <FormLabel htmlFor="30-min" className="font-normal cursor-pointer text-sm">30 minutes</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="60" id="60-min" />
                  <FormLabel htmlFor="60-min" className="font-normal cursor-pointer text-sm">1 hour</FormLabel>
                </div>

                {/* Hour-based intervals */}
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="120" id="120-min" />
                  <FormLabel htmlFor="120-min" className="font-normal cursor-pointer text-sm">2 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="180" id="180-min" />
                  <FormLabel htmlFor="180-min" className="font-normal cursor-pointer text-sm">3 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="240" id="240-min" />
                  <FormLabel htmlFor="240-min" className="font-normal cursor-pointer text-sm">4 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="300" id="300-min" />
                  <FormLabel htmlFor="300-min" className="font-normal cursor-pointer text-sm">5 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="360" id="360-min" />
                  <FormLabel htmlFor="360-min" className="font-normal cursor-pointer text-sm">6 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="420" id="420-min" />
                  <FormLabel htmlFor="420-min" className="font-normal cursor-pointer text-sm">7 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="480" id="480-min" />
                  <FormLabel htmlFor="480-min" className="font-normal cursor-pointer text-sm">8 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="540" id="540-min" />
                  <FormLabel htmlFor="540-min" className="font-normal cursor-pointer text-sm">9 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="600" id="600-min" />
                  <FormLabel htmlFor="600-min" className="font-normal cursor-pointer text-sm">10 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="660" id="660-min" />
                  <FormLabel htmlFor="660-min" className="font-normal cursor-pointer text-sm">11 hours</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="720" id="720-min" />
                  <FormLabel htmlFor="720-min" className="font-normal cursor-pointer text-sm">12 hours</FormLabel>
                </div>
              </RadioGroup>
            </FormControl>
            <FormDescription className="mt-1 text-xs">
              Set the default time interval for new time blocks
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
