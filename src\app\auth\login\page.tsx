"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, LayoutDashboard, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';

const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
});

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });



  async function onSubmit(values: z.infer<typeof loginSchema>) {
    setIsLoading(true);
    setErrorDetails(null);

    try {
      // Ensure password is trimmed
      const trimmedPassword = values.password.trim();

      // Make the login request
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: values.email,
          password: trimmedPassword
        }),
        credentials: 'include',
      });

      // Get the response text
      const responseText = await response.text();

      // Parse the JSON response
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        setErrorDetails('Invalid response from server. Please try again later.');
        throw new Error('Server returned an invalid response');
      }

      // Check if login was successful
      if (!response.ok || !data.success) {
        const errorMessage = data.error?.message || 'Unknown error';
        const errorCode = data.error?.code || 'UNKNOWN_ERROR';

        // Provide user-friendly error messages
        if (errorCode === 'INVALID_CREDENTIALS' || errorCode === 'USER_NOT_FOUND') {
          setErrorDetails('Invalid email or password. Please check your credentials and try again.');
          throw new Error('Invalid email or password');
        } else if (errorCode === 'DATABASE_ERROR') {
          setErrorDetails('Database connection error. Please try again later.');
          throw new Error('Database connection failed');
        } else {
          setErrorDetails(`Login failed: ${errorMessage}`);
          throw new Error(errorMessage || 'Login failed');
        }
      }

      // Extract user data and token
      const responseData = data.data || {};
      const token = responseData.token;
      const user = responseData.user;

      if (!token || !user) {
        setErrorDetails('Server response is missing required data');
        throw new Error('Invalid server response');
      }

      // Store in localStorage for PWA support and offline access
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify({
        id: user.id,
        name: user.name,
        email: user.email,
        lastLogin: new Date().toISOString()
      }));

      // Also store the complete profile
      localStorage.setItem('userProfile', JSON.stringify(user));

      // --- Fix: Clear categories cache/localStorage so next page fetches fresh categories ---
      localStorage.removeItem('categories');
      // --------------------------------------------------------------

      toast.success('Logged in successfully');
      router.push('/home');
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Login</CardTitle>
          <CardDescription>
            Enter your email and password to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {errorDetails && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="mt-2">
                    <div className="text-sm font-medium">Error Details:</div>
                    <div className="text-xs mt-1 break-words">{errorDetails}</div>
                  </AlertDescription>
                </Alert>
              )}

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Please wait
                  </>
                ) : (
                  'Login'
                )}
              </Button>

              <div className="text-xs text-center text-muted-foreground mt-2">
                If you are having trouble logging in, please make sure your email and password are correct.
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Don&apos;t have an account?{' '}
            <Link href="/auth/register" className="font-medium text-primary underline-offset-4 hover:underline">
              Register
            </Link>
          </div>


        </CardFooter>
      </Card>
    </div>
  );
}
