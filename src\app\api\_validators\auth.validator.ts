import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Login request schema
export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Register request schema
export const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

// Password change request schema
export const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
});

/**
 * Validate login request
 */
export async function validateLogin(req: NextRequest): Promise<{ data: z.infer<typeof loginSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = loginSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate register request
 */
export async function validateRegister(req: NextRequest): Promise<{ data: z.infer<typeof registerSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = registerSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate password change request
 */
export async function validatePasswordChange(req: NextRequest): Promise<{ data: z.infer<typeof passwordChangeSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = passwordChangeSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}
