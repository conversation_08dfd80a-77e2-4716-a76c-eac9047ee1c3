"use client"

import { usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth';
import Navbar from '@/components/navbar';
import React from 'react';
import { Loader2 } from 'lucide-react';

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

// Simple loading spinner component with minimal display
function LoadingSpinner() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      {/* Removed loader icon to prevent flashing during navigation */}
    </div>
  );
}

export default function AuthenticatedLayout({
  children,
}: AuthenticatedLayoutProps) {
  const pathname = usePathname();
  // Default redirect for protected routes
  const redirectTo = '/auth/login';
  // Allow demo paths without authentication check if needed
  const allowDemo = pathname.includes('/demo');

  const finalRedirectTo = allowDemo ? undefined : redirectTo;
  const { isAuthenticated, isLoading } = useAuth({ redirectTo: finalRedirectTo });

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // If not authenticated and not on an allowed demo path, prevent rendering children
  if (!isAuthenticated && !allowDemo) {
    return <LoadingSpinner />;
  }

  // Render the layout with Navbar for authenticated users or allowed demo paths
  return (
    <div className="flex min-h-screen flex-col bg-muted/10">
      <Navbar />
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
