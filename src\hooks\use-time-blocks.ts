"use client"

import { useState, useEffect, useCallback, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { TimeBlock, TimeBlockFormData } from '@/lib/types';
import { toast } from 'sonner';
import { usePreferences } from './use-preferences';

// Mock data for demo purposes
const mockTimeBlocks: TimeBlock[] = [
  {
    id: uuidv4(),
    userId: 'user-1',
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '10:30',
    note: 'Morning team meeting to discuss project progress',
    category: 'Work',
    isTodo: true,
    isCompleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: uuidv4(),
    userId: 'user-1',
    date: new Date().toISOString().split('T')[0],
    startTime: '11:00',
    endTime: '12:00',
    note: 'Work on NoteHour app development',
    category: 'Work',
    isTodo: true,
    isCompleted: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: uuidv4(),
    userId: 'user-1',
    date: new Date().toISOString().split('T')[0],
    startTime: '12:00',
    endTime: '13:00',
    note: 'Lunch break',
    category: 'Break',
    isTodo: false,
    isCompleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: uuidv4(),
    userId: 'user-1',
    date: new Date().toISOString().split('T')[0],
    startTime: '14:00',
    endTime: '15:30',
    note: 'Review code and fix bugs',
    category: 'Work',
    isTodo: true,
    isCompleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: uuidv4(),
    userId: 'user-1',
    date: new Date().toISOString().split('T')[0],
    startTime: '16:00',
    endTime: '17:00',
    note: 'Quick workout session',
    category: 'Exercise',
    isTodo: false,
    isCompleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Cache the time blocks data in memory
let cachedTimeBlocks = {
  data: [] as TimeBlock[],
  lastFetched: 0
};

// Default cache time is 2 minutes, but we'll adjust it based on sync preference
const DEFAULT_CACHE_TIME = 2 * 60 * 1000; // 2 minutes
const SYNC_CACHE_TIME = 30 * 1000; // 30 seconds when sync is enabled - less aggressive polling
let isLoadingTimeBlocks = false;
let pendingLoadPromise: Promise<void> | null = null;

export function useTimeBlocks() {
  const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>(cachedTimeBlocks.data);
  const [loading, setLoading] = useState(cachedTimeBlocks.data.length === 0);
  const { preferences } = usePreferences();

  // Helper function to try loading time blocks from localStorage
  const tryLoadFromLocalStorage = useCallback(() => {
    try {
      const storedBlocks = localStorage.getItem('timeBlocks');
      if (storedBlocks) {
        const parsedBlocks = JSON.parse(storedBlocks);
        if (Array.isArray(parsedBlocks)) {
          setTimeBlocks(parsedBlocks);

          // Determine cache time based on sync preference
          const cacheTime = preferences?.syncEnabled ? SYNC_CACHE_TIME : DEFAULT_CACHE_TIME;

          // Update cache
          cachedTimeBlocks = {
            data: parsedBlocks,
            lastFetched: Date.now() - (cacheTime / 2) // Set to half expired so it refreshes soon but not immediately
          };

          return true;
        }
      }
    } catch (e) {
      // Silent fail - will return false below
    }
    return false;
  }, [preferences?.syncEnabled]);

  // Function to load time blocks from API or localStorage
  const loadTimeBlocks = useCallback(async (forceRefresh = false) => {
    // If we're already loading and not forcing a refresh, return the pending promise
    if (!forceRefresh && isLoadingTimeBlocks && pendingLoadPromise) {
      return pendingLoadPromise;
    }

    const now = Date.now();

    // Determine cache time based on sync preference
    const cacheTime = preferences?.syncEnabled ? SYNC_CACHE_TIME : DEFAULT_CACHE_TIME;

    // Return cached value if valid and not forcing refresh
    if (!forceRefresh && cachedTimeBlocks.data.length > 0 && now - cachedTimeBlocks.lastFetched < cacheTime) {
      setTimeBlocks(cachedTimeBlocks.data);
      setLoading(false);
      return Promise.resolve();
    }

    // Only show loading state if we don't have any data yet or if it's a forced refresh
    if (cachedTimeBlocks.data.length === 0 || forceRefresh) {
      setLoading(true);
    }
    isLoadingTimeBlocks = true;

    // Create a new promise for this load operation
    pendingLoadPromise = (async () => {
      try {
        // Try to load from localStorage first if available and cache is expired
        if (!forceRefresh && tryLoadFromLocalStorage()) {
          setLoading(false);
          return;
        }

        // Add a cache-busting query parameter to ensure we always get fresh data
        const cacheBuster = `?_=${Date.now()}`;
        const response = await fetch(`/api/timeblocks${cacheBuster}`, {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
        });

        if (response.ok) {
          const responseData = await response.json();

          // Check if the response has the expected structure with data property
          // The API returns { success: true, data: [...] }
          const rawData = responseData.data || responseData;

          // Ensure data is an array
          if (!Array.isArray(rawData)) {
            throw new Error('Invalid time blocks data format');
          }

          // Validate that each time block has a valid ID and categoryData
          const validatedData = rawData.map((block: any) => {
            // Ensure block has a valid ID
            const validatedBlock = !block.id || typeof block.id !== 'string' || block.id === 'undefined'
              ? {
                  ...block,
                  id: block._id || `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
                }
              : { ...block };

            // Ensure categoryData is properly set if available
            if (block.categoryData) {
              validatedBlock.categoryData = block.categoryData;
            }

            return validatedBlock;
          });

          // Update cache
          cachedTimeBlocks = {
            data: validatedData,
            lastFetched: now
          };

          // Update state
          setTimeBlocks(validatedData);

          // Store in localStorage for offline access
          localStorage.setItem('timeBlocks', JSON.stringify(validatedData));
        } else {
          // Fallback to localStorage
          if (!tryLoadFromLocalStorage()) {
            // Use mock data as last resort
            setTimeBlocks(mockTimeBlocks);
            localStorage.setItem('timeBlocks', JSON.stringify(mockTimeBlocks));
          }
        }
      } catch (error) {
        // Fallback to localStorage
        if (!tryLoadFromLocalStorage()) {
          // Use mock data as last resort
          setTimeBlocks(mockTimeBlocks);
          localStorage.setItem('timeBlocks', JSON.stringify(mockTimeBlocks));
        }
      } finally {
        setLoading(false);
        isLoadingTimeBlocks = false;
        pendingLoadPromise = null;
      }
    })();

    return pendingLoadPromise;
  }, [preferences?.syncEnabled, tryLoadFromLocalStorage]);

  // Load time blocks on component mount
  useEffect(() => {
    // Force refresh on initial load to ensure we get the latest data
    const initialLoad = async () => {
      try {
        await loadTimeBlocks(true);
      } catch (error) {
        console.error('Error loading time blocks:', error);
      }
    };

    initialLoad();
  }, [loadTimeBlocks]);

  // Set up visibility change listener to refresh when user returns to the app
  useEffect(() => {
    // Only refresh on visibility change if sync is enabled
    if (preferences?.syncEnabled) {
      const handleVisibilityChange = () => {
        if (document.visibilityState === 'visible') {
          // User has returned to the app, refresh data
          loadTimeBlocks(true);
        }
      };

      // Add event listener
      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Cleanup on unmount
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    }

    // No cleanup needed if sync is disabled
    return () => {};
  }, [preferences?.syncEnabled, loadTimeBlocks]);

  // Add a new time block
  const addTimeBlock = async (data: TimeBlockFormData) => {
    const toastId = toast.loading('Adding time block...');

    try {
      // Check if user is authenticated by checking localStorage
      const user = localStorage.getItem('user');
      const isAuthenticated = !!user;

      // Make sure isTodo is properly set
      const formData = {
        ...data,
        isTodo: data.isTodo !== undefined ? data.isTodo : false,
        isCompleted: data.isCompleted !== undefined ? data.isCompleted : false
      };

      console.log('Adding time block with data:', formData);

      // Try to save to the server first if authenticated
      if (isAuthenticated) {
        try {
          console.log('User is authenticated, attempting to save to server');

          // Get user ID from localStorage
          let userId = 'user-1'; // Default fallback
          try {
            const userData = JSON.parse(user);
            userId = userData.id || userData._id || 'user-1';
            console.log('Using user ID:', userId);
          } catch (e) {
            console.error('Error parsing user data:', e);
          }

          const response = await fetch('/api/timeblocks', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            },
            credentials: 'include',
            body: JSON.stringify(formData)
          });

          // Log the response status
          console.log('Server response status:', response.status);

          if (response.ok) {
            const responseData = await response.json();
            console.log('Server response data:', responseData);

            // Check if the response has the expected structure with data property
            const newBlock = responseData.data || responseData;

            // Update local cache with the new block
            const updatedBlocks = [...cachedTimeBlocks.data, newBlock];
            cachedTimeBlocks = {
              data: updatedBlocks,
              lastFetched: Date.now()
            };

            // Update state
            setTimeBlocks(updatedBlocks);

            // Update localStorage
            localStorage.setItem('timeBlocks', JSON.stringify(updatedBlocks));

            toast.dismiss(toastId);
            toast.success('Time block added successfully');

            // Force refresh to ensure we have the latest data
            await loadTimeBlocks(true);

            return newBlock;
          } else {
            // If server request fails, get the error details
            const errorData = await response.json();
            console.error('Server error response:', errorData);

            // Check if it's a category error, which is common for new users
            if (errorData.error && errorData.error.message &&
                (errorData.error.message.includes('Category') ||
                 errorData.error.code === 'CATEGORY_NOT_FOUND')) {
              console.log('Category error detected, will create time block locally and suggest creating categories');
              // Don't throw error here - let it fall through to local storage creation
              // This allows new users to create time blocks even without categories
            }

            // For authentication errors, try to refresh the auth
            if (response.status === 401) {
              console.log('Authentication error, falling back to local storage');
              // Continue to local storage fallback
            } else {
              // For other errors, show the specific error message
              toast.dismiss(toastId);
              const errorMessage = errorData.error?.message || 'Failed to add time block';
              toast.error(errorMessage);
              throw new Error(errorMessage);
            }
          }
        } catch (error) {
          console.error('Error saving to server, falling back to local storage:', error);
          // Continue to local storage fallback for network errors
        }
      }

      // Fallback to local storage (for offline mode or if server request failed)
      // Get user ID from localStorage if available
      let userId = 'user-1'; // Default fallback
      if (user) {
        try {
          const userData = JSON.parse(user);
          userId = userData.id || userData._id || 'user-1';
        } catch (e) {
          console.error('Error parsing user data:', e);
        }
      }

      console.log('Creating local time block with user ID:', userId);

      const newBlock: TimeBlock = {
        id: uuidv4(),
        userId: userId,
        ...formData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      console.log('Created new local time block:', newBlock);

      const updatedBlocks = [...timeBlocks, newBlock];

      // Update state
      setTimeBlocks(updatedBlocks);

      // Update cache
      cachedTimeBlocks = {
        data: updatedBlocks,
        lastFetched: Date.now()
      };

      // Update localStorage
      localStorage.setItem('timeBlocks', JSON.stringify(updatedBlocks));

      toast.dismiss(toastId);
      toast.success(isAuthenticated ? 'Time block added successfully (offline)' : 'Time block added successfully');
      return newBlock;
    } catch (error) {
      // For other errors, show the error and don't create a local copy
      console.error('Failed to add time block:', error);
      toast.dismiss(toastId);

      // Show a more specific error message if available
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to add time block. Please try again.';

      if (!toast.message(errorMessage)) {
        toast.error(errorMessage);
      }

      throw error;
    }
  };

  // Update an existing time block
  const updateTimeBlock = async (id: string, data: Partial<TimeBlock>) => {
    const toastId = toast.loading('Updating time block...');
    try {
      console.log('Updating time block with ID:', id, 'Data:', data);

      // Validate ID
      if (!id || id === 'undefined') {
        toast.dismiss(toastId);
        toast.error('Invalid time block ID');
        return false;
      }

      // Find the existing block to get its current boolean flags
      const existingBlock = timeBlocks.find(block => block.id === id);

      // Prepare formData, preserving existing boolean flags if not explicitly provided in `data`
      const formData = {
        ...data,
        isTodo: data.isTodo !== undefined ? data.isTodo : (existingBlock ? existingBlock.isTodo : false),
        isCompleted: data.isCompleted !== undefined ? data.isCompleted : (existingBlock ? existingBlock.isCompleted : false),
      };

      // Check if the ID is a UUID format (local only)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (uuidRegex.test(id)) {
        console.log('UUID format detected, using optimistic update for local-only block');
        const localUpdatedBlocks = timeBlocks.map(block =>
          block.id === id ? { ...block, ...formData, updatedAt: new Date().toISOString() } : block
        );
        setTimeBlocks(localUpdatedBlocks);
        cachedTimeBlocks = { data: localUpdatedBlocks, lastFetched: Date.now() };
        localStorage.setItem('timeBlocks', JSON.stringify(localUpdatedBlocks));
        toast.dismiss(toastId);
        toast.success('Time block updated successfully (local only)');
        return true;
      }

      // Validate MongoDB ObjectId format if it's not a UUID
      if (id.length === 24 && !/^[0-9a-fA-F]{24}$/.test(id)) {
        toast.dismiss(toastId);
        toast.error('Invalid time block ID format');
        return false;
      }

      // Check if user is authenticated
      const user = localStorage.getItem('user');
      const isAuthenticated = !!user;

      if (!isAuthenticated) {
        console.log('User not authenticated, using local update only for non-UUID block');
        const offlineUpdatedBlocks = timeBlocks.map(block =>
          block.id === id ? { ...block, ...formData, updatedAt: new Date().toISOString() } : block
        );
        setTimeBlocks(offlineUpdatedBlocks);
        cachedTimeBlocks = { data: offlineUpdatedBlocks, lastFetched: Date.now() };
        localStorage.setItem('timeBlocks', JSON.stringify(offlineUpdatedBlocks));
        toast.dismiss(toastId);
        toast.success('Time block updated successfully (offline)');
        return true;
      }

      // User is authenticated, proceed with server update
      console.log('Sending update request to server for time block ID:', id, 'Payload:', formData);
      const response = await fetch(`/api/timeblocks/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        credentials: 'include',
        body: JSON.stringify(formData)
      });

      console.log('Server response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Server error response:', errorData);

        if (errorData.error?.code === 'CATEGORY_NOT_FOUND' || errorData.error?.message?.includes('Category')) {
          toast.dismiss(toastId);
          toast.error('Category not found. Please create a category first.');
          return false;
        }

        if (response.status === 401) {
          console.log('Authentication error, falling back to local update');
          const authErrorUpdatedBlocks = timeBlocks.map(block =>
            block.id === id ? { ...block, ...formData, updatedAt: new Date().toISOString() } : block
          );
          setTimeBlocks(authErrorUpdatedBlocks);
          cachedTimeBlocks = { data: authErrorUpdatedBlocks, lastFetched: Date.now() };
          localStorage.setItem('timeBlocks', JSON.stringify(authErrorUpdatedBlocks));
          toast.dismiss(toastId);
          toast.success('Time block updated successfully (offline due to auth error)');
          return true;
        }

        const errorMessage = errorData.error?.message || 'Failed to update time block';
        toast.dismiss(toastId);
        toast.error(errorMessage);
        return false;
      }

      const responseData = await response.json();
      console.log('Server response data:', responseData);
      const updatedBlockFromServer = responseData.data || responseData;

      const serverUpdatedBlocks = timeBlocks.map(block =>
        block.id === id ? { ...block, ...updatedBlockFromServer, updatedAt: new Date().toISOString() } : block
      );
      setTimeBlocks(serverUpdatedBlocks);
      cachedTimeBlocks = { data: serverUpdatedBlocks, lastFetched: Date.now() };
      localStorage.setItem('timeBlocks', JSON.stringify(serverUpdatedBlocks));

      toast.dismiss(toastId);
      toast.success('Time block updated successfully');
      return true;

    } catch (error) {
      console.error('Error updating time block:', error);
      toast.dismiss(toastId);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update time block. Please try again.';
      toast.error(errorMessage);
      return false;
    }
  };

  // Delete a time block
  const deleteTimeBlock = async (id: string) => {
    const toastId = toast.loading('Deleting time block...');
    try {
      // Validate ID
      if (!id || id === 'undefined') {
        toast.dismiss(toastId);
        toast.error('Invalid time block ID');
        return false;
      }

      // Check if the ID is a UUID format (local only)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (uuidRegex.test(id)) {
        console.log('UUID format detected, using optimistic delete for local-only block');
        const localUpdatedBlocks = timeBlocks.filter(block => block.id !== id);
        setTimeBlocks(localUpdatedBlocks);
        cachedTimeBlocks = { data: localUpdatedBlocks, lastFetched: Date.now() };
        localStorage.setItem('timeBlocks', JSON.stringify(localUpdatedBlocks));
        toast.dismiss(toastId);
        toast.success('Time block deleted successfully (local only)');
        return true;
      }

      // Validate MongoDB ObjectId format if it's not a UUID
      if (id.length === 24 && !/^[0-9a-fA-F]{24}$/.test(id)) {
        toast.dismiss(toastId);
        toast.error('Invalid time block ID format');
        return false;
      }

      // Check if user is authenticated (as server deletion requires auth)
      const user = localStorage.getItem('user');
      const isAuthenticated = !!user;

      if (!isAuthenticated) {
        console.log('User not authenticated, cannot delete server-synced time block ID:', id);
        toast.dismiss(toastId);
        toast.error('Authentication required to delete this time block.');
        return false;
      }

      // User is authenticated, proceed with server deletion
      console.log('Sending delete request to server for time block ID:', id);
      const response = await fetch(`/api/timeblocks/${id}`, {
        method: 'DELETE',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        credentials: 'include',
      });

      console.log('Server delete response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Failed to delete time block from server.';
        try {
            const errorData = await response.json();
            errorMessage = errorData.error?.message || errorMessage;
        } catch (e) {
            // Ignore if parsing fails, use default message
        }
        console.error('Server error deleting time block:', response.status, response.statusText);
        toast.dismiss(toastId);
        toast.error(errorMessage);
        return false;
      }

      const serverUpdatedBlocks = timeBlocks.filter(block => block.id !== id);
      setTimeBlocks(serverUpdatedBlocks);
      cachedTimeBlocks = { data: serverUpdatedBlocks, lastFetched: Date.now() };
      localStorage.setItem('timeBlocks', JSON.stringify(serverUpdatedBlocks));

      toast.dismiss(toastId);
      toast.success('Time block deleted successfully');
      return true;

    } catch (error) {
      console.error('Error deleting time block:', error);
      toast.dismiss(toastId);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete time block. Please try again.';
      toast.error(errorMessage);
      return false;
    }
  };

  return {
    timeBlocks,
    loading,
    addTimeBlock,
    updateTimeBlock,
    deleteTimeBlock,
    refreshTimeBlocks: (forceRefresh = false) => loadTimeBlocks(forceRefresh),
  };
}
