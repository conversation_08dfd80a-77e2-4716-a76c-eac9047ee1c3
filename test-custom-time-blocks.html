<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Custom Time Blocks</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>Custom Time Blocks Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Add Custom Time Blocks</h2>
        <p>This will add custom time blocks to your preferences:</p>
        <ul>
            <li>12:00 AM - 4:00 AM (Night)</li>
            <li>8:00 PM - 11:00 PM (Evening)</li>
        </ul>
        <button onclick="addCustomTimeBlocks()">Add Custom Time Blocks</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Get Current Preferences</h2>
        <p>This will fetch your current preferences to see if custom time blocks are saved:</p>
        <button onclick="getCurrentPreferences()">Get Preferences</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Clear Custom Time Blocks</h2>
        <p>This will remove all custom time blocks:</p>
        <button onclick="clearCustomTimeBlocks()">Clear Custom Time Blocks</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        async function addCustomTimeBlocks() {
            const resultDiv = document.getElementById('result1');
            resultDiv.innerHTML = 'Adding custom time blocks...';
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/profile`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        preferences: {
                            customTimeBlocks: [
                                { startTime: '00:00', endTime: '04:00' },
                                { startTime: '20:00', endTime: '23:00' }
                            ]
                        }
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Success!</strong><br>
                        Custom time blocks added successfully.<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Error:</strong> ${data.message || 'Failed to add custom time blocks'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        async function getCurrentPreferences() {
            const resultDiv = document.getElementById('result2');
            resultDiv.innerHTML = 'Fetching preferences...';
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/profile`, {
                    method: 'GET',
                    credentials: 'include'
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Current Preferences:</strong><br>
                        <strong>Custom Time Blocks:</strong> ${data.preferences?.customTimeBlocks ? JSON.stringify(data.preferences.customTimeBlocks, null, 2) : 'None'}<br>
                        <strong>Time Interval:</strong> ${data.preferences?.timeInterval || 'Not set'}<br>
                        <pre>${JSON.stringify(data.preferences, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Error:</strong> ${data.message || 'Failed to fetch preferences'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        async function clearCustomTimeBlocks() {
            const resultDiv = document.getElementById('result3');
            resultDiv.innerHTML = 'Clearing custom time blocks...';
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/profile`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        preferences: {
                            customTimeBlocks: []
                        }
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Success!</strong><br>
                        Custom time blocks cleared successfully.<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Error:</strong> ${data.message || 'Failed to clear custom time blocks'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        // Auto-fetch preferences on page load
        window.onload = function() {
            getCurrentPreferences();
        };
    </script>
</body>
</html>
