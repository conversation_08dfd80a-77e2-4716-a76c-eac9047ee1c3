"use client"

import { useState, useCallback, useEffect, useRef } from "react";
import { toast } from "sonner";
import useS<PERSON> from "swr";
import { useAuth } from "./use-auth";
import { TimeBlock } from "@/lib/types";
import { useTimeBlocks } from "./use-time-blocks";

const fetcher = async (url: string) => {
  const response = await fetch(url, {
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    },
    credentials: 'include'
  });
  if (!response.ok) {
    throw new Error('Failed to fetch todos');
  }
  return response.json();
};

export function useTodos() {
  const { isAuthenticated } = useAuth();
  // Remove global loading spinner for fetches; only use for actions
  const [isLoading, setIsLoading] = useState(false);
  const { timeBlocks, refreshTimeBlocks, addTimeBlock, updateTimeBlock, deleteTimeBlock } = useTimeBlocks();
  const [localTodos, setLocalTodos] = useState<TimeBlock[]>([]);

  // Use SWR for real-time todo management for authenticated users with optimized settings
  const {
    data: remoteTodos = [],
    mutate,
    error,
    isLoading: swrLoading
  } = useSWR<TimeBlock[]>(
    isAuthenticated ? "/api/todos" : null,
    fetcher,
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      refreshInterval: 2000, // Disable polling since we'll use mutate for real-time updates
      dedupingInterval: 2000, // Remove deduping to ensure immediate updates
      keepPreviousData: true,
      revalidateIfStale: true,
      errorRetryCount: 3
    }
  );

  // For authenticated users or as fallback, filter todos from timeBlocks
  useEffect(() => {
    if (!isAuthenticated || timeBlocks.length > 0) {
      // Filter timeBlocks where isTodo is true, including routine todos
      const todoBlocks = timeBlocks.filter(block => block.isTodo === true);

      // For routine todos, make sure we don't include duplicates
      // Keep track of routine IDs we've seen to avoid duplicates
      const seenRoutineIds = new Set();

      // Filter out duplicate routine todos (keep only the first occurrence)
      const uniqueTodoBlocks = todoBlocks.filter(block => {
        // If it's not a routine task, always include it
        if (!block.routineId) return true;

        // If we've seen this routine before, don't include it again
        if (seenRoutineIds.has(block.routineId)) return false;

        // Otherwise, mark this routine as seen and include it
        seenRoutineIds.add(block.routineId);
        return true;
      });

      setLocalTodos(uniqueTodoBlocks);
    }
  }, [timeBlocks, isAuthenticated]);

  // Sync timeBlocks changes with todos immediately
  useEffect(() => {
    if (isAuthenticated) {
      // Immediate revalidation when timeBlocks change
      mutate();
    }
  }, [timeBlocks, isAuthenticated, mutate]);

  // Combined todos from remote and local sources
  // Make sure to include all todos, even those generated from routines
  const todos = isAuthenticated && remoteTodos.length > 0 ? remoteTodos : localTodos;

  // Create a new todo
  const createTodo = useCallback(async (data: Omit<TimeBlock, "id">) => {
    try {
      // Remove loading state for better UX
      // Always ensure isTodo is set to true
      const todoData = {
        ...data,
        isTodo: true
      };

      // Create temporary ID for optimistic update
      const tempId = `temp-${Date.now()}`;
      const optimisticTodo = { ...todoData, id: tempId };

      // Apply optimistic update
      if (isAuthenticated) {
        await mutate([...remoteTodos, optimisticTodo], false);
      } else {
        setLocalTodos([...localTodos, optimisticTodo]);
      }

      // For authenticated users, use the API
      if (isAuthenticated) {
        const response = await fetch("/api/todos", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          credentials: 'include',
          body: JSON.stringify(todoData),
        });

        if (!response.ok) {
          throw new Error("Failed to create todo");
        }

        const newTodo = await response.json();
        // Update the remoteTodos list and refresh the timeBlocks for local state
        await Promise.all([mutate([...remoteTodos, newTodo], false), refreshTimeBlocks()]);
        toast.success("Todo created successfully");
        return newTodo;
      }
      // For non-authenticated users, use local timeBlocks
      else {
        const result = await addTimeBlock(todoData);
        if (result) {
          toast.success("Todo created successfully");
          refreshTimeBlocks();
          return result;
        } else {
          throw new Error("Failed to create local todo");
        }
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create todo");
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, remoteTodos, mutate, refreshTimeBlocks, addTimeBlock])

  // Update a todo
  const updateTodo = useCallback(async (id: string, data: Partial<TimeBlock>) => {
    try {
      // Remove loading state for better UX

      // Find existing todo to preserve isTodo flag if not explicitly provided
      const existingTodo = todos.find(todo => todo.id === id);
      if (!existingTodo) {
        throw new Error("Todo not found");
      }

      // Always preserve isTodo flag
      const updateData = {
        ...data,
        isTodo: data.isTodo !== undefined ? data.isTodo : true
      };

      // Optimistic update
      const updatedTodos = todos.map(todo =>
        todo.id === id ? { ...todo, ...updateData } : todo
      );

      if (isAuthenticated) {
        // For remote todos, update via API
        await mutate(updatedTodos, false);

        const response = await fetch(`/api/timeblocks/${id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          credentials: 'include',
          body: JSON.stringify(updateData),
        });

        if (!response.ok) {
          throw new Error("Failed to update todo");
        }

        const updatedTodo = await response.json();
        await Promise.all([mutate(), refreshTimeBlocks()]); // Revalidate to ensure sync
        toast.success("Todo updated successfully");
        return updatedTodo;
      } else {
        // For local todos, use updateTimeBlock from the destructured hook
        const result = await updateTimeBlock(id, updateData);

        if (result) {
          refreshTimeBlocks();
          toast.success("Todo updated successfully");
          return { ...existingTodo, ...updateData };
        } else {
          throw new Error("Failed to update local todo");
        }
      }
    } catch (error) {
      // Revert on error
      if (isAuthenticated) {
        await mutate();
      } else {
        refreshTimeBlocks();
      }
      toast.error(error instanceof Error ? error.message : "Failed to update todo");
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [todos, mutate, isAuthenticated, refreshTimeBlocks, updateTimeBlock]);

  // Delete a todo
  const deleteTodo = useCallback(async (id: string) => {
    try {
      // Remove loading state for better UX

      // Immediate optimistic delete for UI
      const filteredTodos = todos.filter(todo => todo.id !== id);
      
      if (isAuthenticated) {
        // Optimistic update
        await mutate(filteredTodos, false);
        setLocalTodos(filteredTodos); // Update local state as well for smoother transitions

        try {
          const response = await fetch(`/api/timeblocks/${id}`, {
            method: "DELETE",
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            },
            credentials: 'include'
          });

          if (!response.ok) {
            throw new Error("Failed to delete todo");
          }

          // Force revalidation to ensure consistency
          await Promise.all([
            mutate(undefined, { revalidate: true }),
            refreshTimeBlocks()
          ]);
          toast.success("Todo deleted successfully");
          return true;
        } catch (error) {
          // Revert optimistic update on error
          await mutate(undefined, { revalidate: true });
          throw error;
        }
      } else {
        // For local todos, use deleteTimeBlock from the destructured hook
        const result = await deleteTimeBlock(id);

        if (result) {
          refreshTimeBlocks();
          toast.success("Todo deleted successfully");
          return true;
        } else {
          throw new Error("Failed to delete local todo");
        }
      }
    } catch (error) {
      // Revert on error
      if (isAuthenticated) {
        await mutate();
      } else {
        refreshTimeBlocks();
      }
      toast.error(error instanceof Error ? error.message : "Failed to delete todo");
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [todos, mutate, isAuthenticated, refreshTimeBlocks, deleteTimeBlock]);

  // Toggle todo completion
  const toggleTodoComplete = useCallback(async (id: string, isCompleted: boolean) => {
    try {
      // Optimistic update with immediate UI feedback
      const optimisticData = todos.map(todo =>
        todo.id === id ? { ...todo, isCompleted, isTodo: true } : todo
      );
      
      // Update UI immediately
      if (isAuthenticated) {
        mutate(optimisticData, false);
      } else {
        setLocalTodos(optimisticData);
      }

      // Perform the actual update and ensure immediate state sync
      const updatedTodo = await updateTodo(id, { isCompleted, isTodo: true });
      
      // Immediately update both remote and local states
      if (isAuthenticated) {
        // Update remote state with the confirmed update
        await mutate(
          todos.map(todo => todo.id === id ? { ...todo, ...updatedTodo } : todo),
          false
        );
      }
      
      // Force immediate revalidation of both data sources
      await Promise.all([
        mutate(undefined, { revalidate: true }),
        refreshTimeBlocks()
      ]);

      // Update local state as well for consistency
      setLocalTodos(prevTodos => 
        prevTodos.map(todo => todo.id === id ? { ...todo, isCompleted } : todo)
      );

      return true;
    } catch (error) {
      console.error('Failed to toggle todo completion:', error);
      toast.error('Failed to update todo status');

      // Revert optimistic update on error
      if (isAuthenticated) {
        await mutate();
      } else {
        refreshTimeBlocks();
      }

      return false;
    }
  }, [todos, updateTodo, isAuthenticated, mutate, refreshTimeBlocks, setLocalTodos]);

  // Track last conversion time to throttle calls
  const lastConversionTimeRef = useRef(0);

  // Convert routines to todos with throttling to prevent too many API calls
  const convertRoutinesToTodos = useCallback(async (force = false) => {
    try {
      // Throttle to at most once every 30 seconds unless forced
      const now = Date.now();
      const timeSinceLastCall = now - lastConversionTimeRef.current;

      if (!force && timeSinceLastCall < 30000 && lastConversionTimeRef.current !== 0) {
        console.log('Skipping routine conversion - throttled');
        return { converted: 0, message: 'Throttled' };
      }

      setIsLoading(true);
      lastConversionTimeRef.current = now;

      const response = await fetch('/api/routines/convert-to-todos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to convert routines to todos');
      }

      const result = await response.json();

      // Force refresh the todos list to include newly created routine todos
      // Use cache: false to ensure we get fresh data from the server
      await fetch('/api/todos', {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        credentials: 'include'
      });

      // Then update the SWR cache with completely fresh data
      await mutate(undefined, { revalidate: true });

      if (result.converted > 0) {
        console.log(`Created ${result.converted} todos from routines`);
      } else {
        console.log('No new todos to create from routines');
      }

      return result;
    } catch (error) {
      console.error('Failed to convert routines to todos:', error);
      toast.error('Failed to convert routines to todos');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [mutate]);

  // Maintain a ref to track if routines have been converted to todos already
  const routinesConvertedRef = useRef(false);

  // Only fetch data on initial mount to avoid excessive revalidations
  useEffect(() => {
    if (isAuthenticated && !routinesConvertedRef.current) {
      // Mark as converted to prevent repeated calls
      routinesConvertedRef.current = true;
      // Delay initial load slightly to allow UI to render first
      const timer = setTimeout(() => {
        mutate(undefined, { revalidate: true });
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, mutate]);

  // Improved throttled refresh with better performance characteristics
  const lastRefreshTimeRef = useRef(0);
  const pendingRefreshRef = useRef<Promise<any> | null>(null);

  const refresh = useCallback(() => {
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTimeRef.current;

    // If we have a pending refresh, return that instead of creating a new one
    if (pendingRefreshRef.current) {
      return pendingRefreshRef.current;
    }

    // Throttle refreshes to at most once every 10 seconds
    if (timeSinceLastRefresh < 10000 && lastRefreshTimeRef.current !== 0) {
      console.log('Skipping refresh - throttled');
      return Promise.resolve();
    }

    lastRefreshTimeRef.current = now;
    // Create a new refresh promise
    const refreshPromise = mutate(undefined, { revalidate: true })
      .finally(() => {
        // Clear the pending refresh when done
        pendingRefreshRef.current = null;
      });

    // Store the promise for potential reuse
    pendingRefreshRef.current = refreshPromise;
    return refreshPromise;
  }, [mutate]);

  return {
    todos,
    // Only show loading spinner for the very first load
    isLoading: swrLoading,
    error,
    createTodo,
    updateTodo,
    deleteTodo,
    toggleTodoComplete,
    convertRoutinesToTodos,
    refresh
  };
}
