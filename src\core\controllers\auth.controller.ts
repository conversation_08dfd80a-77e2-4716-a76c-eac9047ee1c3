import { NextResponse } from 'next/server';
import { authService } from '@/core/services';
import { LoginRequest, RegisterRequest, PasswordChangeRequest } from '@/types/auth';
import { successResponse, errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';
import { ApiError } from '@/core/errors/api-error';

/**
 * Authentication controller
 */
export class AuthController {
  /**
   * Register a new user
   * @param data Registration data
   * @returns Response with success message or error
   */
  async register(data: RegisterRequest): Promise<NextResponse> {
    try {
      const result = await authService.register(data);
      return successResponse({ message: result.message }, result.status);
    } catch (error) {
      logger.error('Registration controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Login a user
   * @param data Login data
   * @returns Response with token and user data or error
   */
  async login(data: LoginRequest): Promise<NextResponse> {
    try {
      return await authService.login(data);
    } catch (error) {
      logger.error('Login controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Logout a user
   * @returns Response with success message
   */
  async logout(): Promise<NextResponse> {
    try {
      return authService.logout();
    } catch (error) {
      logger.error('Logout controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Get user profile
   * @param userId User ID
   * @returns Response with user profile or error
   */
  async getProfile(userId: string): Promise<NextResponse> {
    try {
      const profile = await authService.getProfile(userId);
      return successResponse(profile);
    } catch (error) {
      logger.error('Get profile controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Update user profile
   * @param userId User ID
   * @param data Profile data
   * @returns Response with updated profile or error
   */
  async updateProfile(userId: string, data: any): Promise<NextResponse> {
    try {
      const profile = await authService.updateProfile(userId, data);
      return successResponse(profile);
    } catch (error) {
      logger.error('Update profile controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Change user password
   * @param userId User ID
   * @param data Password change data
   * @returns Response with success message or error
   */
  async changePassword(userId: string, data: PasswordChangeRequest): Promise<NextResponse> {
    try {
      await authService.changePassword(userId, data);
      return successResponse({ message: 'Password changed successfully' });
    } catch (error) {
      logger.error('Change password controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }
}
